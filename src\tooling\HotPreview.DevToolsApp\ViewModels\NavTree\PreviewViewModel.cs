using HotPreview.Tooling;
using Microsoft.UI.Xaml.Controls;

namespace HotPreview.DevToolsApp.ViewModels.NavTree;

public class PreviewViewModel : NavTreeItemViewModel
{
    private readonly MainPageViewModel _mainPageViewModel;

    public PreviewViewModel(MainPageViewModel mainPageViewModel, UIComponentTooling uiComponent, PreviewTooling preview)
    {
        _mainPageViewModel = mainPageViewModel;
        UIComponent = uiComponent;
        Preview = preview;

        UpdateSnapshotsCommand = new RelayCommand(async () =>
        {
            AppManager? appManager = _mainPageViewModel.CurrentApp;
            if (appManager is not null)
            {
                // Remember the current selection
                NavTreeItemViewModel? currentSelection = _mainPageViewModel.CurrentSelection;

                await appManager.UpdatePreviewSnapshotsAsync(UIComponent, Preview);

                // Navigate back to the previously selected item if it was a PreviewViewModel
                if (currentSelection is PreviewViewModel selectedPreview)
                {
                    appManager.NavigateToPreview(selectedPreview.UIComponent, selectedPreview.Preview);
                }
                else if (currentSelection is UIComponentViewModel selectedComponent && selectedComponent.UIComponent.HasSinglePreview)
                {
                    appManager.NavigateToPreview(selectedComponent.UIComponent, selectedComponent.UIComponent.DefaultPreview);
                }
            }
        });
    }

    public UIComponentTooling UIComponent { get; }
    public PreviewTooling Preview { get; }

    public override string DisplayName => Preview.DisplayName;
    public override string PathIcon => UIComponent.PathIcon;

    public override ICommand UpdateSnapshotsCommand { get; }

    public override void OnItemInvoked()
    {
        // Navigate to the preview, for all app connections that have the preview
        _mainPageViewModel.CurrentApp?.NavigateToPreview(UIComponent, Preview);
    }
}
